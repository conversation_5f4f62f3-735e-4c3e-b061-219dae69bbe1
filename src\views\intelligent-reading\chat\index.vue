<template>
  <a-row class="chat-container">
    <!-- 左侧AI问答模块 -->
    <a-col :span="rightPanelCollapsed ? 23 : 18" class="left-panel">
      <div class="chat-box">
        <!-- 默认欢迎界面 -->
        <div v-if="isWelcome" class="default-container">
          <img src="/src/assets/images/logo1.png" class="logo" />
          <!-- 输入发送区域 -->
          <div class="default-input-area">
            <a-textarea v-model:value="inputContent" placeholder="请输入你的问题..." class="input-text" @keyup.enter="sendMessage" />
            <img class="send-icon" @click="sendMessage" src="/src/assets/images/send.png" />
          </div>
        </div>
        <div v-else-if="currentDialogMessages.length !== 0" class="message-area">
          <!-- 消息展示区域 -->
          <div class="message-container" ref="messageContainerRef">
            <div v-for="(message, index) in currentDialogMessages" :key="index" :class="['message-bubble', message.role]">
              <img v-if="message.role === 'ai'" class="avatar" src="/src/assets/images/ai.png" />
              <div class="message-content">
                <!--user-问题列表-->
                <template v-if="message.role === 'user'">
                  <span> {{ message.content }} </span>
                </template>
                <!-- ai-回答列表 -->
                <template v-if="message.role === 'ai'">
                  <div v-if="showThought">正在思考中<a-spin size="small" style="margin-left: 5px" /></div>
                  <!--思考过程-->
                  <div v-if="!showThought && showAfterThought">
                    <h1>思考过程</h1>
                    <CodeEditor v-model:value="message.thought" :mode="'application/json'" />
                  </div>
                  <div class="error-text" v-if="!showThought"
                    >{{ message.content }}
                    <!--图表显示-->
                    <div v-if="message.showChart">
                      <div>
                        <h1>表格</h1>
                        <a-table :columns="message.tableColumns" :data-source="message.tableData" bordered />
                      </div>
                      <!--                      面积图-->
                      <!--                      response_area_chart-->
                      <div v-if="!(message.chartType === 'response_table')">
                        <h1>图表</h1>
                        <!-- 绑定到 message.chartType 和 message.chartOptions -->
                        <ChartComponent :type="message.chartType" :data="message.chartOptions" />
                      </div>
                    </div>
                  </div>

                  <!-- 这是需要触发getTableChart的View按钮 -->
                  <a-button class="toggle-details-btn" @click="getTableChart" v-if="message.role === 'ai' && showTable">
                    <template #icon>
                      <CaretRightOutlined />
                    </template>
                    View results
                  </a-button>
                </template>
              </div>
            </div>
          </div>
        </div>
        <div v-if="!isWelcome" class="bottom-div">
          <a-button class="new-dialogue-btn" @click="startChat">
            <img src="/src/assets/images/newdialogue.png" class="new-dialogue-img" />
            开启新对话
          </a-button>
          <!-- 输入发送区域 -->
          <div class="input-area">
            <a-textarea v-model:value="inputContent" placeholder="输入你的问题..." class="input-text" @keyup.enter="sendMessage" />
            <img class="send-icon" @click="sendMessage" src="/src/assets/images/send.png" />
          </div>
        </div>
      </div>
    </a-col>

    <!-- 右侧历史/收藏模块 -->
    <a-col :span="rightPanelCollapsed ? 1 : 6" class="right-panel">
      <div class="history-container" :class="{ collapsed: rightPanelCollapsed }">
        <div class="top-toolbar" v-if="!rightPanelCollapsed">
          <a-button class="collapse-button" @click="toggleRightPanel" :icon="h(MenuUnfoldOutlined)" />
          <!-- 选项卡切换 -->
          <a-menu v-model:selectedKeys="current" mode="horizontal" :items="items" class="nav-menu" @select="handleMenuSelect" />
        </div>
        <!-- 对话列表 -->
        <a-list :data-source="activeTab === 'history' ? historyList : collectionList" class="dialog-list" v-if="!rightPanelCollapsed">
          <template #renderItem="{ item }">
            <a-list-item class="dialog-item" @click="handleDialogClick(item)" :class="{ 'selected-item': item.selected }">
              <span @click.stop="toggleCollect(item)" class="star-icon">
                <StarFilled v-if="item.collected" :style="{ color: '#ffd700' }" />
                <StarOutlined v-else :style="{ color: '#ccc' }" />
              </span>
              <span class="headline">{{ item.headline }}</span>
              <DeleteOutlined class="delete-icon" @click.stop="handleDelete(item)" />
            </a-list-item>
          </template>
        </a-list>
        <div v-if="rightPanelCollapsed" class="collapsed-state">
          <a-button class="collapse-button" @click="toggleRightPanel" :icon="h(MenuFoldOutlined)" />
        </div>
      </div>
    </a-col>
  </a-row>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, onUnmounted, onBeforeUnmount, nextTick, watch, h } from 'vue';
  import {
    SendOutlined,
    StarOutlined,
    StarFilled,
    DeleteOutlined,
    BulbOutlined,
    SlackOutlined,
    ClockCircleOutlined,
    MenuFoldOutlined,
    MenuUnfoldOutlined,
  } from '@ant-design/icons-vue';
  import { useGlobSetting } from '@/hooks/setting';
  import { useUserStore } from '/@/store/modules/user';
  import { message } from 'ant-design-vue';
  import _ from 'lodash';
  import { getChatHistoryApi, getChatListApi, getChatCollectionListApi, updateChatCollectApi, deleteChatApi } from '@/api/intelligent-reading/chat';
  import { CodeEditor } from '@/components/CodeEditor';
  import ChartComponent from '../component/chart.vue';
  //图表参数
  const chartType = ref('line');
  const chartOptions = ref({});
  //获取userid
  const userStore = useUserStore();
  const userId = userStore.userInfo.id;
  // 左侧问答数据
  const inputContent = ref('');
  const chartRefs = ref([]);
  const charts = ref([]);
  const isWelcome = ref(true);

  const tableColumns = ref([]);
  const tableData = ref([]);
  const isNewDialog = ref(false);
  const isLoadingHistory = ref(false);
  const messageContainerRef = ref(null);

  const rightPanelCollapsed = ref(false);
  const toggleRightPanel = () => {
    rightPanelCollapsed.value = !rightPanelCollapsed.value;
  };
  //新建对话id，对话列表，新建对话需置空
  const threadId = ref(null);
  const dialogStore = reactive({
    // 当前选中对话ID
    selectedDialogId: null,
    // 所有对话数据
    dialogs: [],
  });
  const scrollToBottom = () => {
    if (messageContainerRef.value) {
      messageContainerRef.value.scrollTop = messageContainerRef.value.scrollHeight;
    }
  };
  //是否显示表格按钮
  const showTable = ref(false);
  //是否显示思考过程
  const showAnswer = ref(true);

  //显示正在思考
  const showThought = ref(false);
  //显示思考内容
  const showAfterThought = ref(false);
  // 当前显示的消息列表
  const currentDialogMessages = computed(() => {
    if (!dialogStore.selectedDialogId) return [];
    const dialog = dialogStore.dialogs.find((d) => d.id == dialogStore.selectedDialogId);
    return dialog?.messages || [];
  });
  watch(
    () => currentDialogMessages.value,
    (newMessages) => {
      console.log('消息变化:', newMessages);
      // newMessages.forEach((message, index) => {
      //   // 仅处理AI消息且包含图表数据的情况
      //   if (message.role === 'ai' && message.content?.chart) {
      //     initChart(index, message.content.chart);
      //   }
      // });
    },
    { deep: true, immediate: true }
  );
  // 生成新的对话ID
  const generateNewDialogId = () => {
    const existingIds = dialogStore.dialogs.map((d) => d.id);
    if (existingIds.length === 0) return 1;
    const maxId = Math.max(...existingIds);
    return isFinite(maxId) ? maxId + 1 : 1;
  };
  const startChat = () => {
    resetChatState();
    newDialogue();
  };
  const resetChatState = () => {
    // 1. 清除所有选中状态
    historyList.forEach((i) => (i.selected = false));
    collectionList.value.forEach((i) => (i.selected = false));

    // 2. 重置当前对话
    dialogStore.selectedDialogId = null;
    threadId.value = null;
    inputContent.value = '';

    // 3. 强制显示欢迎界面
    isWelcome.value = true;

    // 4. 关闭可能存在的SSE连接
    eventSourceClose();
  };
  const newDialogue = () => {
    // 清除之前所有选中的状态
    historyList.forEach((i) => (i.selected = false));
    collectionList.value.forEach((i) => (i.selected = false));

    // 生成新的对话ID
    const newId = generateNewDialogId();
    //开启新对话时，重置threadId为空
    threadId.value = null;
    // 创建空消息的对话
    const newDialog = {
      id: newId,
      threadId: null,
      headline: `智读问答`,
      messages: [],
    };

    // 添加到对话列表
    dialogStore.dialogs.push(newDialog);

    // 设置为当前选中的对话
    // historyList[0].selected = true;
    dialogStore.selectedDialogId = newId;
    isNewDialog.value = true;
  };
  // 右侧面板数据
  // 模拟数据
  const historyList = reactive([]);

  const collectionList = ref([]);
  // 当前激活的选项卡
  const activeTab = computed(() => current.value[0]);

  const current = ref(['history']);
  const items = ref([
    { key: 'history', label: '历史对话' },
    // { key: 'collection', label: '收藏对话' },
  ]);

  // 解析历史消息中的AI回答
  const parseAnswerString = (answer) => {
    const result = {
      text: '',
      thought: '',
      showChart: false,
      chartType: '',
      tableColumns: [],
      tableData: [],
      chartOptions: {},
    };

    if (!answer) return result;

    // 解析思考过程
    try {
      const parsed = JSON.parse(answer);
      if (parsed.thoughts) {
        result.thought = JSON.stringify(parsed.thoughts, null, 2);
        return result;
      }
    } catch (e) {
      // 非JSON格式继续处理
    }

    // 解析图表数据
    if (answer.includes('<chart-view')) {
      result.showChart = true;
      const contentMatch = answer.match(/content="({.+?})"/);

      if (contentMatch) {
        try {
          const jsonStr = contentMatch[1]
            .replace(/&quot;/g, '"')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>');

          const data = JSON.parse(jsonStr);

          // 生成表格数据
          result.tableColumns = generateTableColumns(data.data);
          result.tableData = data.data;
          result.chartType = data.type;

          // 生成图表配置
          const fieldTypes = detectFieldTypes(data.data);

          if (data.type === 'response_line_chart') {
            const dateField = Object.keys(fieldTypes).find((k) => fieldTypes[k] === 'date');
            const numberFields = Object.keys(fieldTypes).filter((k) => fieldTypes[k] === 'number');
            const nameField = Object.keys(fieldTypes).find((k) => fieldTypes[k] === 'string');

            const xAxisData = [...new Set(data.data.map((item) => item[dateField]))].sort();
            const series = numberFields.map((field) => ({
              name: field,
              type: 'line',
              data: data.data.map((item) => item[field]),
              symbolSize: 8,
            }));

            result.chartOptions = {
              xAxis: { type: 'category', data: xAxisData },
              yAxis: { type: 'value' },
              series,
            };
          }
          // 其他图表类型处理...
        } catch (e) {
          console.error('解析图表数据失败:', e);
        }
      }

      // 提取文本内容
      const textPart = answer.split('<chart-view')[0];
      result.text = textPart?.replace('data:', '')?.replace(/\\n/g, '\n')?.trim() || '';
    } else {
      // 纯文本处理
      result.text = answer.replace(/\\n/g, '\n');
    }

    return result;
  };

  // 处理对话点击
  const handleDialogClick = async (item) => {
    if (!item) return;
    console.log('item', item);

    if (!item.threadId) {
      // 清除所有对话的选中状态
      historyList.forEach((i) => (i.selected = false));
      collectionList.value.forEach((i) => (i.selected = false));
      // 设置当前选中对话
      item.selected = true;
      dialogStore.selectedDialogId = item.id;
      isWelcome.value = false;
      return; // 直接返回，不发送请求
    }
    threadId.value = item.threadId;
    // 清除所有对话的选中状态
    historyList.forEach((i) => (i.selected = false));
    collectionList.value.forEach((i) => (i.selected = false));
    // 1. 设置当前选中对话
    item.selected = true;
    dialogStore.selectedDialogId = item.id;
    isLoadingHistory.value = true;
    isWelcome.value = false;
    // 2. 检查是否已加载过该对话的消息
    const currentDialog = dialogStore.dialogs.find((d) => d.id === item.id);
    // 3. 如果对话消息为空，从API加载
    if (!currentDialog || currentDialog.messages.length === 0) {
      try {
        // 3. 调用接口获取历史记录
        const result = await getChatHistoryApi({
          threadId: item.threadId,
        });
        console.log('res ', result);
        // 4. 转换数据结构
        const convertedMessages = [];
        // 遍历chatList数组
        if (result.chatList && result.chatList.length > 0) {
          for (const chat of result.chatList) {
            // 用户消息
            convertedMessages.push({
              role: 'user',
              content: chat.question,
              timestamp: chat.timestamp || new Date().toISOString(),
            });

            // AI消息 - 使用parseAnswerString解析
            const parsedAnswer = parseAnswerString(chat.answer);

            convertedMessages.push({
              role: 'ai',
              content: parsedAnswer.text || chat.answer, // 确保总有内容显示
              thought: parsedAnswer.thought,
              showChart: parsedAnswer.showChart,
              chartType: parsedAnswer.chartType,
              tableColumns: parsedAnswer.tableColumns,
              tableData: parsedAnswer.tableData,
              chartOptions: parsedAnswer.chartOptions,
              timestamp: chat.timestamp || new Date().toISOString(),
            });
          }
        }

        // 5. 更新当前对话消息
        if (currentDialog) {
          currentDialog.messages = convertedMessages;
        } else {
          dialogStore.dialogs.push({
            id: item.id,
            threadId: item.threadId,
            headline: item.headline,
            messages: convertedMessages,
          });
        }
      } catch (e) {
        console.error('获取历史记录失败:', e);
        // 显示错误提示
        const currentDialog = dialogStore.dialogs.find((d) => d.id === item.id);
        if (currentDialog) {
          currenokenDialog.messages.push({
            role: 'ai',
            content: { text: '获取历史记录失败，请稍后重试' },
            isError: true,
          });
        }
      }
      isLoadingHistory.value = false;
      // 6. 滚动到底部
      nextTick(() => {
        scrollToBottom();
      });
    }
  };

  // 切换收藏状态
  const toggleCollect = async (item) => {
    console.log('tid :', item.threadId);
    try {
      const result = await updateChatCollectApi({
        threadId: item.threadId, // 作为第一个参数
        userId, // 作为第二个参数
        status: item.collected ? 0 : 1, // 作为第三个参数
      });
      // 调用API更新收藏状态
      console.log('res:', result);
      // 更新本地状态
      const target = historyList.find((i) => i.id === item.id);
      if (target) {
        target.collected = !target.collected;
      }
      // 更新收藏列表
      if (activeTab.value === 'collection') {
        if (!item.collected) {
          // 如果之前未收藏，现在收藏了，添加到收藏列表
          collectionList.value.unshift({
            ...item,
            collected: true,
          });
        } else {
          // 如果之前已收藏，现在取消收藏，从收藏列表中移除
          const index = collectionList.value.findIndex((i) => i.id === item.id);
          if (index !== -1) {
            collectionList.value.splice(index, 1);
          }
        }
      }
    } catch (e) {
      console.error('更新收藏状态失败:', e);
    }
  };

  // 处理菜单选择
  const handleMenuSelect = ({ key }) => {
    current.value = [key];
  };
  const handleDelete = async (item) => {
    console.log('item', item);
    // 调用删除接口
    const result = await deleteChatApi({ threadId: item.threadId });
    console.log('result', result);
    console.log('current.value', current.value);
    //更新对话列表
    fetchChatList();
    //更新收藏列表
    // fetchCollectionList();
    // // 1. 从历史列表删除
    // const historyIndex = historyList.findIndex((i) => i.id === item.id);
    // if (historyIndex > -1) historyList.splice(historyIndex, 1);
    //
    // // 2. 从收藏列表删除
    // const collectIndex = collectionList.value.findIndex((i) => i.id === item.id);
    // if (collectIndex > -1) collectionList.value.splice(collectIndex, 1);

    // 3. 从对话存储中删除
    const dialogIndex = dialogStore.dialogs.findIndex((d) => d.id === item.id);
    if (dialogIndex > -1) dialogStore.dialogs.splice(dialogIndex, 1);

    // 4. 如果删除的是当前选中对话
    // 如果删除的是当前选中对话
    if (dialogStore.selectedDialogId === item.id) {
      dialogStore.selectedDialogId = null;
      // 无论是否有其他对话，都显示欢迎界面
      isWelcome.value = true;
    }
    // 提示删除成功
    message.success('对话已删除');
  };
  // 发送消息
  const sendMessage = () => {
    if (!inputContent.value.trim()) return;
    if (isWelcome.value === true) {
      isWelcome.value = false;
    }
    // 如果没有选中对话，先创建新对话
    if (!dialogStore.selectedDialogId) {
      newDialogue();
    }

    // 获取当前对话
    const currentDialog = dialogStore.dialogs.find((d) => d.id == dialogStore.selectedDialogId);
    if (isNewDialog.value) {
      // 创建摘要作为标题
      const summary = inputContent.value.trim().substring(0, 20);
      const newHeadline = summary + (inputContent.value.length > 20 ? '...' : '');

      // 更新对话标题
      currentDialog.headline = newHeadline;

      // 将新对话添加到历史列表
      historyList.unshift({
        id: currentDialog.id,
        threadId: currentDialog.threadId,
        headline: newHeadline,
        content: newHeadline,
        collected: false,
        selected: true,
      });

      // 重置新对话标志
      isNewDialog.value = false;
    }
    // 更新历史列表中的对话内容（如果是新对话）
    const historyItem = historyList.find((item) => item.id === currentDialog.id);
    if (historyItem && historyItem.headline === '智读问答') {
      // 截取前20个字符作为标题
      const summary = inputContent.value.trim().substring(0, 20);
      const newHeadline = summary + (inputContent.value.length > 20 ? '...' : '');

      // 更新对话标题
      currentDialog.headline = newHeadline;
      historyItem.headline = newHeadline;

      // 更新内容摘要
      historyItem.content = newHeadline;
    }
    // 添加用户消息
    currentDialog.messages.push({
      role: 'user',
      content: inputContent.value,
      isStreaming: true,
    });
    // //user
    // currentDialogMessages.value.push({
    //   title: inputContent.value,
    //   messages: [{ role: 'user', content: inputContent.value }],
    // });
    // 添加初始AI消息占位符
    currentDialog.messages.push({
      role: 'ai',
      content: '', // 初始加载动画
      isStreaming: true,
      isError: false,
      thought: '',
    });
    nextTick(() => {
      scrollToBottom();
      inputContent.value = '';
    });
    connectSSE();
  };

  //模拟数据流
  // 存储接收到的数据
  const error = ref(null);
  let eventSource = null;
  const { domainUrl } = useGlobSetting();

  const processSSEData = async (line, currentDialog) => {
    const dataStr = line.replace('data:`json\\n', '').replace('\\n', '').replace('`json', '').replace(/\\n/g, '\n');
    if (!line) return;
    // 处理线程ID
    if (line.startsWith('threadId=')) {
      const receivedThreadId = line.split('=')[1];
      // 更新对话threadId
      currentDialog.threadId = receivedThreadId;
      threadId.value = receivedThreadId;
      // 同步更新历史记录
      const historyItem = historyList.find((item) => item.id === currentDialog.id);
      if (historyItem) {
        historyItem.threadId = receivedThreadId;
      }
      return;
    }
    // 处理JSON数据
    try {
      const aiMessage = currentDialog.messages.find((msg) => msg.role === 'ai' && msg.isStreaming);
      aiMessage.status = '';

      /*显示思考过程*/
      if (dataStr.includes('thoughts')) {
        const jsonStr = dataStr
          .replace(/^(data:|`json\\n)/, '')
          .replace(/`$/, '')
          .replace(/\\n/g, '\n')
          .replace(/`json/, '')
          .replace(/\\`/, '');
        showThought.value = false;
        showAfterThought.value = true;
        // 处理可能的多行JSON字符串
        aiMessage.thought = jsonStr;
      } else if (dataStr.includes('NO DATA') || dataStr.includes('failed')) {
        aiMessage.content = dataStr.replace('data:', '');
        showAfterThought.value = false;
        showThought.value = false;
      } else {
        showAfterThought.value = false;
        showThought.value = false;
        //显示文本内容
        aiMessage.content = extractDataContent(dataStr);

        /*判断图表类型*/
        if (dataStr.indexOf('<chart-view')) {
          aiMessage.showChart = true;
          // 尝试解析内容
          const contentMatch = dataStr.match(/content="({.+?})"/);
          if (contentMatch) {
            const jsonStr = contentMatch[1]
              .replace(/&quot;/g, '"')
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>');
            const data = JSON.parse(jsonStr);

            aiMessage.tableColumns = generateTableColumns(data.data);
            aiMessage.tableData = data.data;
            aiMessage.chartType = data.type;

            //判断图表中key是否是number、string、data
            const fieldTypes = detectFieldTypes(data.data);
            console.log('fieldTypes', fieldTypes);
            if (data.type === 'response_table') {
              aiMessage.chartType = 'response_table';
            } else if (data.type === 'response_line_chart') {
              aiMessage.chartType = 'response_line_chart';
              if (data.data) {
                const dataItem = data.data;
                // 1. 提取字段类型
                const dateField = Object.keys(fieldTypes).find((k) => fieldTypes[k] === 'date');
                const numberFields = Object.keys(fieldTypes).filter((k) => fieldTypes[k] === 'number');
                const nameField = Object.keys(fieldTypes).find((k) => fieldTypes[k] === 'string');
                // 获取所有唯一的日期（X轴数据）
                const xAxisData = [...new Set(dataItem.map((item) => item[dateField]))].sort();
                const series = numberFields.map((field) => ({
                  name: field,
                  type: 'line',
                  data: dataItem.map((item) => item[field]),
                  symbolSize: 8,
                  label: {
                    show: true,
                    formatter: (params) => {
                      return dataItem[params.dataIndex][nameField];
                    },
                  },
                }));
                chartType.value = 'line';
                chartOptions.value = {
                  xAxis: {
                    type: 'category',
                    data: xAxisData,
                  },
                  yAxis: {
                    type: 'value',
                  },
                  series: series,
                };
              }
            } else if (data.type === 'response_pie_chart') {
              aiMessage.chartType = 'response_pie_chart';
              const numberFields = Object.keys(fieldTypes).find((k) => fieldTypes[k] === 'number');
              if (data.data) {
                const dataItem = data.data;
                chartType.value = 'pie';
                const pieData = dataItem.map((item) => {
                  // 过滤出非数值字段（根据业务需求调整）
                  const textFields = Object.entries(item)
                    .filter(([key, value]) => typeof value !== 'number')
                    .map(([_, value]) => value);

                  return {
                    name: textFields.join(' - '), // 用分隔符拼接所有文本字段
                    value: item[numberFields],
                  };
                });
                const series = [
                  {
                    name: '',
                    type: 'pie',
                    data: pieData,
                  },
                ];
                chartOptions.value = {
                  series: series,
                };
                console.log('chartOptions', chartOptions.value);
              }
            } else if (data.type === 'response_bar_chart') {
              aiMessage.chartType = 'response_bar_chart';
              const numberFields = Object.keys(fieldTypes).filter((k) => fieldTypes[k] === 'number');
              const nameFields = Object.keys(fieldTypes).filter((k) => fieldTypes[k] === 'string');
              console.log('numberFields', numberFields);
              console.log('nameFields', nameFields);
              if (data.data) {
                const dataItem = data.data;
                console.log('dataItem', dataItem);
                chartType.value = 'bar';
                // 拼接所有 string 字段作为 X 轴
                const xAxisData = dataItem.map((item) => nameFields.map((field) => item[field]).join(' - '));

                // 使用第一个 number 字段作为 Y 轴（可扩展为多系列）
                const seriesData = dataItem.map((item) => item[numberFields[0]]);

                const series = [
                  {
                    name: '',
                    type: 'bar',
                    data: seriesData,
                  },
                ];
                chartOptions.value = {
                  xAxis: xAxisData,
                  series: series,
                };
                console.log('chartOptions', chartOptions.value);
              }
            }
            console.log('JSON.parse(jsonStr)', JSON.parse(jsonStr));
          }
        }
      }

      // } else if (parsed.done) {
      //   aiMessage.isStreaming = false;
      //   showTable.value = true;
      //   eventSource.close();
      // }
    } catch (e) {
      console.error('JSON解析失败:', e);
    }
  };
  /**
   * 从包含特定标记的字符串中提取data部分内容
   * @param inputString 包含data和chart-view标记的原始字符串
   * @returns 提取出的data部分内容
   */
  const extractDataContent = (inputString) => {
    // 定义开始和结束标记
    const startMarker = 'data:';
    const endMarker = '<chart-view';

    // 查找开始位置
    const startIndex = inputString.indexOf(startMarker);
    if (startIndex === -1) {
      throw new Error('未找到data起始标记');
    }

    // 查找结束位置
    const endIndex = inputString.indexOf(endMarker, startIndex);
    if (endIndex === -1) {
      throw new Error('未找到chart-view结束标记');
    }

    // 提取并返回内容（去掉首尾空白）
    return inputString.slice(startIndex, endIndex).trim().replace(/\\n/g, '\n').replace('data:', '');
  };
  /**
   * 根据数据动态生成表格列配置
   * @param data 表格数据
   * @returns 生成的列配置数组
   */
  const generateTableColumns = (data) => {
    if (!data || data.length === 0) return [];

    // 获取第一个对象的所有key作为列名
    const firstItem = data[0];
    const columnKeys = Object.keys(firstItem);
    return columnKeys.map((key) => {
      // 基础列配置
      const column = {
        title: key,
        dataIndex: key,
        key: key,
      };
      return column;
    });
  };
  /**
   * 判断字段类型
   * @param {Array} data 数据数组
   * @returns {Object} 字段类型映射 { 字段名: 类型 }
   */
  function detectFieldTypes(data) {
    if (!data || data.length === 0) return {};

    const firstItem = data[0];
    const fieldTypes = {};

    Object.keys(firstItem).forEach((field) => {
      const values = data.map((item) => item[field]).filter((val) => val !== undefined);

      if (isNumericField(values)) {
        fieldTypes[field] = 'number';
      } else if (isDateField(values)) {
        fieldTypes[field] = 'date';
      } else {
        fieldTypes[field] = 'string';
      }
    });

    return fieldTypes;
  }

  // 判断是否为数值字段
  function isNumericField(values) {
    return values.every((value) => {
      // 排除布尔值、空字符串和null
      if (typeof value === 'boolean' || value === '' || value === null) return false;

      // 数字或数字字符串
      // return !isNaN(value) && isFinite(value);
      return typeof value === 'number' && isFinite(value);
    });
  }

  // 判断是否为日期字段
  function isDateField(values) {
    const dateFormats = [
      /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z?$/, // ISO 8601
      /^\d{4}-\d{2}-\d{2}$/, // 简单日期
    ];

    return values.every((value) => {
      if (typeof value !== 'string') return false;
      // 检查格式或是否能被Date解析
      return dateFormats.some((format) => format.test(value)) || !isNaN(new Date(value).getTime());
    });
  }

  // 初始化 SSE 连接
  const connectSSE = () => {
    let url;
    console.log('threadId', threadId.value);
    if (threadId.value === null || threadId.value === undefined) {
      // 新建会话
      url = `${domainUrl}/smartReadingDB/chat?token=${encodeURIComponent(userStore.getToken)}&problem=${encodeURIComponent(
        inputContent.value
      )}&userId=${userId}`;
    } else {
      // 历史会话
      url = `${domainUrl}/smartReadingDB/chat?token=${encodeURIComponent(userStore.getToken)}&problem=${encodeURIComponent(
        inputContent.value
      )}&userId=${userId}&threadId=${threadId.value}`;
    }

    // // 只有当threadId有有效值时才添加参数
    // if (threadId.value) {
    //   url += `&threadId=${threadId.value}`;
    // }
    // 创建EventSource
    eventSource = new EventSource(url);
    // 初始化流式消息
    const currentDialog = dialogStore.dialogs.find((d) => d.id == dialogStore.selectedDialogId);
    console.log('currentDialog', currentDialog);
    console.log('dialogStore.selectedDialogId', dialogStore.selectedDialogId);

    eventSource.onmessage = (event) => {
      showThought.value = true;
      processSSEData(event.data, currentDialog);
      // event.data.split('\n').forEach((rawLine) => {
      //   processSSEData(rawLine.trim(), currentDialog);
      // });
      nextTick(scrollToBottom);
    };

    eventSource.onerror = (error) => {
      console.error('SSE连接错误:', error);
      // const currentDialog = dialogStore.dialogs[dialogStore.selectedDialogId];
      const aiMessage = currentDialog.messages.find((msg) => msg.role === 'ai' && msg.isStreaming);
      if (aiMessage) {
        // aiMessage.content = '服务连接异常，请稍后重试';
        aiMessage.isError = true;
        aiMessage.isStreaming = false;
      }
      eventSource.close();
    };
  };
  //关闭sse
  const eventSourceClose = () => {
    if (eventSource) {
      eventSource.close();
    }
  };
  //获取图表和表格信息
  const getTableChart = async () => {
    eventSourceClose();
    // 获取当前对话
    const currentDialog = dialogStore.dialogs.find((d) => d.id == dialogStore.selectedDialogId);

    let para = {
      responseId: currentDialog.responseId,
      userId: userId,
    };
    const result = await getChatingChartApi(para);
    console.log('result', result);

    // 提取预览数据
    const previewData = result.previewData;

    // 1. 构造表格数据
    const tableData = {
      columns: previewData.columns.map((col) => col.name),
      rows: previewData.data.map((row) => ({ [previewData.columns[0].name]: row[0] })),
    };

    // 2. 构造图表数据
    const chartSchema = result.chartDetail.chartSchema;
    const chartData = {
      // 提取 Vega-Lite 中的字段信息
      field: chartSchema.encoding.x.field || chartSchema.encoding.color.field,
      type: chartSchema.encoding.x.type || 'nominal',
      title: chartSchema.title,
      // 生成计数数据
      categories: previewData.data.map((item) => item[0]),
      values: previewData.data.map(() => 1), // 每个名称计数为1
    };

    // 3. 更新最后一条AI消息
    const aiMessages = currentDialog.messages.filter((msg) => msg.role === 'ai');
    const lastAiMessage = aiMessages[aiMessages.length - 1];

    if (lastAiMessage) {
      // 保留已有内容，添加表格和图表数据
      lastAiMessage.content = {
        ...(typeof lastAiMessage.content === 'object' ? lastAiMessage.content : { text: lastAiMessage.content }),
        tableData,
        chart: chartData,
        showDetails: true,
      };
    }

    // 4. 重新初始化图表
    nextTick(() => {
      const index = currentDialog.messages.indexOf(lastAiMessage);
      if (index !== -1) {
        initChart(index, chartData);
      }
    });
  };
  // 获取对话列表
  const fetchChatList = async () => {
    try {
      const result = await getChatListApi({ userId: userId });
      if (result && result.length > 0) {
        // 清空原有历史列表
        historyList.length = 0;

        // 映射API数据到历史列表
        result.forEach((item) => {
          historyList.push({
            id: item.threadId, // 使用threadId作为唯一ID
            threadId: item.threadId,
            headline: item.title,
            content: item.content,
            collected: item.isCollection === 1, // 转换收藏状态
          });
        });
      }
      if (dialogStore.selectedDialogId) {
        const selectedItem = historyList.find((item) => item.id === dialogStore.selectedDialogId);
        if (selectedItem) selectedItem.selected = true;
      }
    } catch (e) {
      console.error('获取对话列表失败:', e);
    }
  };
  // 获取收藏对话列表
  const fetchCollectionList = async () => {
    try {
      const result = await getChatCollectionListApi({ userId: userId });
      collectionList.value = result.map((item) => ({
        id: item.threadId,
        threadId: item.threadId,
        headline: item.title,
        content: item.content,
        collected: true, // 收藏列表中的项都是已收藏
        isCollection: item.isCollection,
      }));
    } catch (e) {
      console.error('获取收藏列表失败:', e);
    }
  };
  // 在组件挂载时获取对话列表
  onMounted(() => {
    fetchChatList();
    fetchCollectionList();
  });

  // 组件卸载时关闭连接
  onUnmounted(() => {
    if (eventSource) {
      eventSource.close();
      console.log('SSE 连接已关闭');
    }
  });
</script>

<style scoped>
  .chat-container {
    height: 86vh;
    padding: 20px;
    position: relative;
    background: rgba(252, 252, 252, 1);
    display: flex;
  }

  .left-panel {
    height: 100%;
    padding-right: 20px;
    display: flex;
    flex-direction: column;
  }
  .right-panel {
    border-radius: 8px;
    background: rgba(252, 252, 252, 1);
    padding: 0 !important; /* 移除内边距 */
    height: 99%;
    box-shadow: none;
    display: flex; /* 添加flex布局 */
    flex-direction: column; /* 垂直方向排列 */
  }
  .chat-box {
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: 1; /* 占据所有可用空间 */
  }
  .message-area {
    flex: 1; /* 占据剩余空间 */
    display: flex;
    flex-direction: column;
    min-height: 0; /* 关键：允许内容收缩 */
  }
  .default-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1; /* 占据整个空间 */
  }
  .history-container {
    padding: 0 10px; /* 添加左右内边距 */
    height: 100%;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
  }
  .top-toolbar {
    display: flex;
    align-items: center;
    padding-bottom: 10px;
  }
  .collapse-button {
    margin-top: 10px;

    border: none;
    box-shadow: none;
    background: transparent;
  }
  .collapsed-state {
    display: flex;
    justify-content: center;
    padding-top: 10px;
  }
  .logo {
    height: 180px;
    width: 180px;
  }
  .message-container {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    border-radius: 4px;
  }
  .process-steps {
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    margin: 15px 20px;
    background: #fafafa;
  }

  .step-item {
    padding: 12px 20px;
    border-bottom: 1px solid #eee;
  }

  .step-item:last-child {
    border-bottom: none;
  }

  .step-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .step-number {
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border-radius: 50%;
    background: #0364ff;
    color: white;
    margin-right: 10px;
  }

  .step-title {
    margin: 0;
    font-size: 15px;
    color: #333;
  }

  .model-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .model-tag {
    background: rgba(3, 100, 255, 0.1);
    color: #0364ff;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
  }

  .thinking-list {
    margin: 8px 0 0 10px;
    color: #666;
    font-size: 13px;
  }

  .thinking-list li {
    margin-bottom: 6px;
    line-height: 1.5;
  }

  .sql-code {
    background: #fff;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    font-family: monospace;
    color: #333;
    overflow-x: auto;
    margin: 10px 0;
  }
  .analysis-text {
    margin-left: 30px;
    margin-top: 10px;
    margin-bottom: 5px;
    font-size: 16px;
  }
  .error-text {
    padding: 10px;
    margin-bottom: 5px;
    font-size: 16px;
  }
  .data-table {
    margin: 15px 30px;
    overflow-x: auto;
  }
  .data-table table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .data-table th,
  .data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
  }

  .data-table th {
    background-color: #fafafa;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }

  .data-table tr:hover {
    background-color: #fafafa;
  }
  .toggle-details-btn {
    margin: 10px 25px;
    color: rgba(3, 100, 255, 1);
    border: none;
    background: transparent;
    padding: 4px 8px;
    transition: all 0.3s;
  }
  .streaming-spinner {
    position: absolute;
    right: -30px;
    top: 50%;
    transform: translateY(-50%);
  }
  .message-bubble.ai {
    position: relative; /* 为加载动画定位提供参照 */
    padding-right: 10px; /* 给加载动画留出空间 */
  }
  .loading-indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #0364ff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    margin-left: 8px;
  }
  .toggle-details-btn:hover {
    background: rgba(3, 100, 255, 0.1);
  }

  .toggle-details-btn:active {
    transform: scale(0.98);
  }

  .toggle-details-btn span {
    margin-left: 4px;
    font-size: 13px;
  }
  .chart-container {
    height: 300px;
    margin-top: 5px; /* 顶部间距减少 */
    width: 800px !important;
    min-width: 600px;
    border: none;
    position: relative;
  }
  .message-bubble {
    margin: 10px 0;
    max-width: 60%;
  }

  .message-bubble.user {
    margin-left: auto;
    text-align: right;
    margin-right: 20px;
  }
  .message-bubble.ai {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    max-width: 100%; /* 设置最大宽度 */
    min-width: 150px; /* 设置最小宽度 */
    width: fit-content; /* 根据内容自动调整宽度 */
    margin-right: 20px; /* 留出滚动条空间 */
  }
  .message-content {
    display: inline-block;
    flex: 1;
    padding: 8px 12px;
    border-radius: 15px;
    background: #f0f2f5;
    max-width: 800px; /* 根据内容自动扩展的最大宽度 */
    white-space: pre-wrap; /* 保持文本换行 */
    word-break: break-word; /* 长单词自动换行 */
  }
  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    flex-shrink: 0;
    margin-right: 4px;
  }
  .selected-item {
    background: rgba(196, 200, 204, 0.2) !important;
    box-shadow: 0 2px 8px rgba(3, 100, 255, 0.1) !important;
  }
  .message-bubble.ai .message-content {
    background: rgba(252, 252, 252, 1);

    flex: 1;
    /* 改为块级元素 */
    display: block;
    /* 移除最大宽度限制 */
    max-width: none;
  }
  .message-bubble.user .message-content {
    background: rgba(3, 100, 255, 1);
    color: white;
    border: 1px solid rgba(227, 227, 227, 1);
    border-radius: 12px;

    font-size: 15px;
  }

  .default-input-area {
    display: flex;
    height: 150px;
    width: 676px;
    position: relative;
    margin: 20px 0;
    gap: 10px;
    border: 1px solid rgba(3, 100, 255, 1);
    border-radius: 8px;
    background: rgba(252, 252, 252, 1);
  }
  .default-input-area:deep(.ant-input) {
    resize: none;
  }
  .default-input-area:deep(.ant-input:focus) {
    box-shadow: none !important;
    border-color: rgba(3, 100, 255, 1) !important;
  }
  .input-text {
    border: none;
    margin-left: 10px;
    margin-top: 10px;
    background: rgba(252, 252, 252, 1);
  }
  .input-area {
    display: flex;
    height: 150px;
    gap: 10px;
    top: 670px;
    border: 1px solid rgba(3, 100, 255, 1);
    border-radius: 8px;
    background: rgba(252, 252, 252, 1);
  }
  .input-area :deep(.ant-input) {
    resize: none;
  }
  .input-area :deep(.ant-input:focus) {
    box-shadow: none !important;
    border-color: rgba(3, 100, 255, 1) !important;
  }
  .deep-thinking-btn {
    border-radius: 108px;
    background: rgba(255, 255, 255, 1);
    border: 0.5px solid rgba(221, 225, 230, 1);
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.05);
    color: rgba(147, 150, 153, 1);
    margin-bottom: 10px;
    height: 30px;
    width: 110px;
  }
  .deep-thinking-btn:hover {
    color: rgba(147, 150, 153, 1);
    border: 0.5px solid rgba(221, 225, 230, 1);
    background: rgb(232, 232, 232);
  }
  .active-deep-think {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(3, 100, 255, 1);
    color: rgba(3, 100, 255, 1);
  }
  .new-dialogue-btn {
    height: 40px;
    width: 120px;
    border-radius: 8px;
    background: linear-gradient(135deg, rgba(79, 82, 255, 1) 0%, rgba(52, 155, 255, 1) 100%);
    box-shadow: 3px 3px 11px 4px rgba(90, 132, 173, 0.25);
    display: inline-block;
    padding: 5px 10px 5px 10px;
    color: white;
    left: 45%;
    margin-bottom: 10px;
  }
  .new-dialogue-btn:hover {
    color: white;
  }
  .new-dialogue-img {
    height: 20px;
    width: 20px;
    margin-right: 5px;
  }
  .send-icon {
    z-index: 9999; /* 确保在最上层 */
    height: 25px;
    width: 25px;
    margin-top: 110px;
    margin-right: 15px;
  }
  .send-btn {
    width: 80px;
  }

  .dialog-list {
    flex: 1; /* 占据剩余所有空间 */
    overflow-y: auto; /* 添加垂直滚动条 */
    margin-top: 15px;
    padding: 10px;
    padding-bottom: 10px;
    height: 0; /* 关键：使flex布局正确计算 */
  }

  .dialog-item {
    width: 100%;
    padding: 12px 15px;
    border-radius: 8px;
    background: rgba(252, 252, 252, 1);
    border: none;
    margin-bottom: 10px;
    display: block; /* 改为块级显示 */
    transition: all 0.3s ease;
    box-sizing: border-box; /* 包含内边距和边框 */
  }
  .dialog-item:hover {
    background: rgba(3, 100, 255, 0.05) !important;
    box-shadow: none;
  }
  .dialog-item.selected-item:hover {
    background: rgba(196, 200, 204, 0.2) !important;
    box-shadow: none;
    transform: none;
  }
  :where(.css-dev-only-do-not-override-1s8knlx).ant-list-split .ant-list-item:last-child {
    border-bottom: 1px solid #e8e8e8 !important;
  }
  .nav-menu :deep(.ant-menu-item-selected) {
    color: rgba(41, 173, 230, 1);
    font-weight: 500;
  }

  .headline {
    font-size: 14px;
    font-weight: bold;
    margin-left: 10px;
  }
  .content {
    font-size: 12px;
    font-weight: 300;
    color: rgba(147, 150, 153, 1);
  }
  .delete-icon {
    position: absolute;
    margin-top: 7px;
    right: 0;
    margin-right: 12px;
    font-size: 14px;
  }
  .meta-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .star-icon {
    cursor: pointer;
    font-size: 16px;
  }

  .tab-switch {
    margin-bottom: 15px;
  }
  .nav-menu {
    width: 100%; /* 横向填满容器 */
    border: none !important; /* 移除默认边框 */
  }

  /* 调整菜单项样式 */
  .nav-menu :deep(.ant-menu-item) {
    flex: 1; /* 等分剩余空间 */
    text-align: center; /* 文字居中 */
    padding: 0 10px !important;
  }
  @media (max-width: 768px) {
    .default-input-area {
      width: 90%;
    }
    .message-container {
      padding: 10px;
    }
  }
  /*对话框置底*/
  .bottom-div {
    margin-top: auto; /* 固定在底部 */
  }
</style>
